--[[
  Media类 - 游戏媒体与界面状态
  负责处理游戏的媒体展示、UI元素和特效渲染
  作为游戏状态系统的一部分，用于显示游戏的媒体信息和更新界面
]]--

Media = Object:extend()  -- 从Object基类扩展出Media类
Media:implement(State)   -- 实现State接口，使其可以作为游戏状态

-- 初始化函数
-- 设置Media对象的初始状态
-- 参数name: 状态名称
function Media:init(name)
  self:init_state(name)  -- 调用状态初始化方法
end


-- 状态进入函数
-- 当游戏进入Media状态时调用
-- 初始化UI、效果和主要游戏元素
-- 参数from: 上一个状态
function Media:on_enter(from)
  -- 设置摄像机位置到屏幕中心
  camera.x, camera.y = gw/2, gh/2
  
  -- 创建三个主要的对象组，用于分层管理游戏元素
  self.main = Group()    -- 主要游戏对象组
  self.effects = Group() -- 特效对象组
  self.ui = Group()      -- UI对象组

  -- 设置背景颜色为蓝色
  graphics.set_background_color(blue[0])
  
  -- 创建文本显示，添加到UI组
  -- 包含游戏标题和副标题
  Text2{group = self.ui, x = gw/2, y = gh/2, lines = {
    {text = '[fg]SNKRX', font = fat_font, alignment = 'center', height_offset = -15},
    {text = '[fg]loop update', font = pixul_font, alignment = 'center'},
  }}
end


-- 状态更新函数
-- 每帧调用，更新所有游戏元素
-- 参数dt: 距离上一帧的时间间隔（秒）
function Media:update(dt)
  -- 更新所有对象组，应用slow_amount影响游戏速度
  -- slow_amount是全局变量，用于控制游戏速度（如慢动作效果）
  self.main:update(dt*slow_amount)    -- 更新主要游戏对象
  self.effects:update(dt*slow_amount) -- 更新特效
  self.ui:update(dt*slow_amount)      -- 更新UI元素
end


-- 状态绘制函数
-- 每帧调用，负责渲染所有可见元素
-- 按照层级顺序绘制：主要对象 -> 特效 -> UI
function Media:draw()
  -- 按顺序绘制各个对象组
  self.main:draw()    -- 绘制主要游戏对象
  self.effects:draw() -- 绘制特效
  self.ui:draw()      -- 绘制UI元素

  -- 在屏幕左上角位置(30,30)绘制佣兵图标
  -- 参数说明: x, y, 角度, x缩放, y缩放, 原点x, 原点y, 颜色
  mercenary:draw(30, 30, 0, 1, 1, 0, 0, yellow2[-5])
end
